package com.eduhk.sa.config;

import com.eduhk.api.sso.SsoConfig;
import com.eduhk.sa.config.jwt.JWTAuthenticationEntryPoint;
import com.eduhk.sa.config.jwt.JWTAuthenticationFilter;
import com.eduhk.sa.config.jwt.JWTCookieUtil;
import com.eduhk.sa.config.jwt.JWTTokenProvider;
import com.eduhk.sa.constant.Role;
import com.eduhk.sa.exceptions.UnauthorizedException;
import com.eduhk.sa.security.OtpAuthenticationFilter;
import com.eduhk.sa.security.OtpAuthenticationProvider;
import com.eduhk.sa.security.SsoPreAuthUserDetailsService;
import com.eduhk.sa.service.SystemSetupService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.support.ErrorPageFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationProvider;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.csrf.CsrfTokenRepository;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.OrRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import javax.annotation.PostConstruct;
import javax.servlet.http.Cookie;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Collections;

@EnableWebSecurity
public class WebSecurityConfig {

    @Bean
    public FilterRegistrationBean<ErrorPageFilter> disableErrorPageFilterForAdmission() {
        FilterRegistrationBean<ErrorPageFilter> filterRegistrationBean = new FilterRegistrationBean<>(new ErrorPageFilter());
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setOrder(Integer.MIN_VALUE);

        filterRegistrationBean.addInitParameter("skipPaths", "/admission/**");
        return filterRegistrationBean;
    }

    // VMSA Configuration
    @Configuration
    @Order(1)
    public static class JWTWebSecurityConfig extends WebSecurityConfigurerAdapter {

        @Autowired
        private JWTTokenProvider tokenProvider;

        @Autowired
        private JWTCookieUtil cookieUtil;

        @Autowired
        private JWTAuthenticationEntryPoint jwtAuthenticationEntryPoint;

        @Autowired
        private SecurityConfiguration configuration;

        @Autowired
        private EnvConfig envConfig;

        @Autowired
        private CsrfTokenRepository csrfTokenRepository;

        @Override
        protected void configure(HttpSecurity http) throws Exception {
            JWTAuthenticationFilter jwtFilter = new JWTAuthenticationFilter(tokenProvider, cookieUtil);
            http
                    .antMatcher("/admission/**")
                    .cors().configurationSource(getCorsSource())
                    .and()
                    .exceptionHandling()
                    .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                    .and()
                    .authorizeRequests()
                    .antMatchers("/admission/login/**").permitAll()
                    .antMatchers("/admission/document/**").permitAll()
                    .antMatchers("/admission/assets/**", "/admission/static/**").permitAll()
                    .antMatchers("/admission/refresh-token").permitAll()
                    .antMatchers("/admission/admin/**").hasAuthority(Role.ADMIN.getValue())
                    .antMatchers("/admission/super-admin/**").hasAuthority(Role.SUPER_ADMIN.getValue())
                    .antMatchers("/admission/applicant/**").hasAuthority(Role.APPLICANT.getValue())
                    .antMatchers("/admission/**", "/admission").permitAll()
                    .anyRequest().authenticated()
                    .and()
                    .logout()
                    .logoutUrl("/admission/logout")
                    .deleteCookies("JSESSIONID")
                    .invalidateHttpSession(true)
                    .permitAll()
                    .logoutSuccessHandler((request, response, authentication) -> {
                        cookieUtil.clearAccessTokenCookie(response);
                        cookieUtil.clearRefreshTokenCookie(response);
                        Cookie jwtCookie = new Cookie("jwt", "");
                        jwtCookie.setPath("/");
                        jwtCookie.setMaxAge(0);
                        jwtCookie.setHttpOnly(false);
                        response.addCookie(jwtCookie);
                        String contextPath = request.getContextPath();
                        if (authentication instanceof PreAuthenticatedAuthenticationToken) {
                            response.sendRedirect(envConfig.getLogoutUrl());
                        }
                        else {
                            response.sendRedirect(contextPath + "/admission/non-eduhk-logout");
                        }
                    })
                    .and()
                    .exceptionHandling()
                    .authenticationEntryPoint((request, response, authException) -> {
                        response.sendRedirect(request.getContextPath() + "/admission");
                    })
                    .accessDeniedPage("/accessDenied")
                    .and()
                    .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class)
                    .headers().frameOptions().sameOrigin();

            if (!envConfig.isCsrfEnabled()) {
                http.csrf().disable();
            } else {
                http.csrf().csrfTokenRepository(csrfTokenRepository);
            }
        }

        private CorsConfigurationSource getCorsSource() {
            return getCorsConfigurationSource(configuration);
        }

    }

    // SSO Configuration
    @Configuration
    @Order(2)
    public class SsoWebSecurityConfig extends WebSecurityConfigurerAdapter {

        @Autowired
        private Constant constant;

        @Autowired
        private SsoConfig ssoConfig;

        @Autowired
        private SsoPreAuthUserDetailsService ssoPreAuthUserDetailsService;

        @Autowired
        private SystemSetupService systemSetupService;

        @Autowired
        private SecurityConfiguration configuration;

        @Autowired
        private EnvConfig envConfig;

        @Autowired
        private CsrfTokenRepository csrfTokenRepository;

        @PostConstruct
        public void init() throws UnsupportedEncodingException {
            String fullLogoutUrl = systemSetupService.getSystemSetupValueByCode("LOGOUT_LINK");
            SsoConfig.setLogoutURL(fullLogoutUrl); // e.g., "https://uoam.ied.edu.hk/oam/server/logout?end_url=https://uappl04.eduhk.hk/VMS/login"
            SsoConfig.setLocalUserName(constant.getLocalUserName());
        }

        @Override
        protected void configure(HttpSecurity http) throws Exception {
            RequestMatcher requestMatchers = new OrRequestMatcher(
                    new AntPathRequestMatcher("/login/eduhk/**"),
                    new AntPathRequestMatcher("/admission/login/eduhk/**")
            );

            http
                    .cors().configurationSource(getCorsSource())
                    .and()
                    .requestMatcher(requestMatchers)
                    .addFilter(ssoConfig.customRequestHeaderAuthenticationFilter(constant.getLocalUserName(), false))
                    .authenticationProvider(preAuthenticatedAuthenticationProvider())
                    .authorizeRequests()
                    .antMatchers("/login/eduhk/**").authenticated()
                    .antMatchers("/admission/login/eduhk/**").authenticated()
                    .and()
                    .exceptionHandling()
                    .authenticationEntryPoint(ssoConfig.authenticationEntryPoint());

            if (!envConfig.isCsrfEnabled()) {
                http.csrf().disable();
            }else{
                http.csrf().csrfTokenRepository(csrfTokenRepository);
            }
        }

        @Bean
        public PreAuthenticatedAuthenticationProvider preAuthenticatedAuthenticationProvider() {
            PreAuthenticatedAuthenticationProvider provider = new PreAuthenticatedAuthenticationProvider();
            provider.setPreAuthenticatedUserDetailsService(ssoPreAuthUserDetailsService);
            return provider;
        }

        private CorsConfigurationSource getCorsSource() {
            return getCorsConfigurationSource(configuration);
        }
    }

    // VMS Configuration
    @Configuration
    @Order(3)
    public class GeneralWebSecurityConfig extends WebSecurityConfigurerAdapter {

        @Autowired
        private OtpAuthenticationProvider otpAuthenticationProvider;

        @Autowired
        private SecurityConfiguration configuration;

        @Autowired
        private EnvConfig envConfig;

        @Autowired
        private CsrfTokenRepository csrfTokenRepository;

        @Override
        protected void configure(HttpSecurity http) throws Exception {
            OtpAuthenticationFilter otpFilter = new OtpAuthenticationFilter(otpAuthenticationManager());
            http
                    .cors().configurationSource(getCorsSource())
                    .and()
                .addFilterBefore(otpFilter, UsernamePasswordAuthenticationFilter.class)
                .authorizeRequests()
                    .antMatchers("/login", "/login/nonEduhk/**", "/login/sendOtp", "/login/verifyOtp", "/logout-complete").permitAll()
                    .antMatchers("/assets/**", "/captcha", "/**.jsp", "/**.pdf", "/error/**").permitAll()
                    .antMatchers("/pdf/**").permitAll()
                    .antMatchers("/", "/index.html", "/assets/**", "/favicon.ico", "/images/**", "/fonts/**", "/static/**").permitAll()
                    .antMatchers("/").permitAll()
                    .antMatchers("/vision/**").permitAll()
                    .antMatchers("/admin/visa/**").authenticated()
                    .antMatchers("/admin/**").authenticated()
                    .antMatchers("/mustache/**").permitAll()
                    .antMatchers("/ajax/**").permitAll()
                    .antMatchers("/uploadVisa/**").authenticated()
                    .antMatchers("/swagger-ui/**", "/v3/api-docs/**").access(envConfig.isSwaggerEnabled() ? "permitAll" : "denyAll")
                    .anyRequest().authenticated()
                .and()
                .logout()
                    .logoutUrl("/logout")
                    .deleteCookies("JSESSIONID")
                    .invalidateHttpSession(true)
                    .permitAll()
                    .logoutSuccessHandler((request, response, authentication) -> {
                        String contextPath = request.getContextPath(); // e.g., "/VMS"
                        if (authentication instanceof PreAuthenticatedAuthenticationToken) {
                            // SSO user: redirect to the SSO logout URL
                            response.sendRedirect(SsoConfig.getLogoutURL());
                        } else if (request.getRequestURI().contains("/admission")){
                            response.sendRedirect(envConfig.getLogoutUrl());
                        }
                        else {
                            // OTP user: redirect to login
                            response.sendRedirect(contextPath + "/login");
                        }
                    })
                .and()
                .exceptionHandling()
                    .authenticationEntryPoint((request, response, authException) -> {
                        response.setStatus(response.getStatus());
                        String message = "Unauthorized Access";
                        if (authException instanceof UnauthorizedException) {
                            message = ((UnauthorizedException) authException).getCustomMessage();
                        }
                        response.getWriter().write(message);
                    })
                    .accessDeniedPage("/accessDenied")
                .and()
                .headers().frameOptions().sameOrigin();

            if (!envConfig.isCsrfEnabled()) {
                http.csrf().disable();
            }else{
                http.csrf().csrfTokenRepository(csrfTokenRepository);
            }
        }

        @Bean
        public AuthenticationManager otpAuthenticationManager() {
            return new ProviderManager(Collections.singletonList(otpAuthenticationProvider));
        }

        private CorsConfigurationSource getCorsSource() {
            return getCorsConfigurationSource(configuration);
        }
    }

    @Bean
    public CsrfTokenRepository csrfTokenRepository() {
        return CookieCsrfTokenRepository.withHttpOnlyFalse();
    }

    private static CorsConfigurationSource getCorsConfigurationSource(SecurityConfiguration configuration) {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.setAllowedOrigins(Arrays.asList(configuration.getAllowedOrigins()));
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");
        config.addExposedHeader("Content-Disposition");
        source.registerCorsConfiguration("/**", config);
        return source;
    }
}