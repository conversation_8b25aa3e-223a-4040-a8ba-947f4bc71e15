package com.eduhk.sa.config.swagger;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.OpenApiCustomiser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "swagger.enabled", havingValue = "true", matchIfMissing = false)
public class SwaggerConfig {

    @Value("${swagger.application.name:VMS API}")
    private String applicationName;

    @Value("${swagger.api-version:1.0}")
    private String apiVersion;

    @Value("${swagger.description:Operations related to VMS}")
    private String apiDescription;

    @Bean
    public OpenAPI customOpenAPI() {
        final String securitySchemeName = "bearerAuth";

        return new OpenAPI()
                .info(new Info()
                        .title(applicationName)
                        .description(apiDescription + "\n\n" +
                                "**JWT Token Authentication:**\n" +
                                "- Use the 'Authorize ' button to set JWT token\n" +
                                "- Available roles: ADMIN, SUPER_ADMIN, APPLICANT")
                        .version(apiVersion))
                .addSecurityItem(new SecurityRequirement().addList(securitySchemeName))
                .components(new Components()
                        .addSecuritySchemes(securitySchemeName, new SecurityScheme()
                                .name(securitySchemeName)
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("Enter JWT token,the token will be synced into cookies.")
                        )
                );
    }
}