package com.eduhk.sa.config.swagger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import com.eduhk.sa.config.jwt.JWTCookieUtil;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

@Component
@ConditionalOnProperty(name = "swagger.enabled", havingValue = "true")
@Profile({"local", "dev"})
@Order(2)
public class SwaggerJwtCookieFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(SwaggerJwtCookieFilter.class);

    @Value("${swagger.jwt-cookie-name:jwt}")
    private String jwtCookieName;

    @Value("${swagger.jwt:jwt}")
    private String jwt;

    @Autowired
    private JWTCookieUtil cookieUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        handleSwaggerJwtCookieSync(request, response);

        filterChain.doFilter(request, response);
    }

    private void handleSwaggerJwtCookieSync(HttpServletRequest request, HttpServletResponse response) {
        try {

            String bearerToken = request.getHeader("Authorization");
            if (!StringUtils.hasText(bearerToken) || !bearerToken.startsWith("Bearer ")) {
                return;
            }

            String headerToken = bearerToken.substring(7);
            if (!StringUtils.hasText(headerToken)) {
                return;
            }

            if (response.isCommitted()) {
                log.debug("Response already committed, cannot set JWT cookie");
                return;
            }

            String currentJwtCookie = getCurrentJwtCookie(request);

            if (!headerToken.equals(currentJwtCookie)) {
                Cookie jwtCookie = new Cookie(jwtCookieName, headerToken);
                jwtCookie.setPath("/");
                jwtCookie.setMaxAge(7200);
                jwtCookie.setHttpOnly(false);
                jwtCookie.setSecure(request.isSecure());
                response.addCookie(jwtCookie);

                log.info("Swagger JWT cookie synced from Authorization header for URI: {}", request.getRequestURI());
            }
        } catch (Exception e) {
            log.debug("Could not sync Swagger JWT cookie: {}", e.getMessage());
        }
    }

    private String getCurrentJwtCookie(HttpServletRequest request) {
        if (request.getCookies() != null) {
            for (Cookie cookie : request.getCookies()) {
                if (jwtCookieName.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return !(uri.contains("/swagger-ui") ||
                uri.contains("/v3/api-docs") ||
                uri.startsWith("/VMS/admission"));
    }
}