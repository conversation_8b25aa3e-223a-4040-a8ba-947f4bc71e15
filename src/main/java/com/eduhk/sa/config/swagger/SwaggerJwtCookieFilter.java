package com.eduhk.sa.config.swagger;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Profile;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import com.eduhk.sa.util.JWTCookieUtil;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

/**
 * Swagger JWT Cookie Filter - Development Only
 * Handles JWT cookie synchronization for Swagger UI in development environment
 * This filter is separate from the main JWT authentication to keep development
 * logic isolated from production authentication logic.
 */
@Component
@ConditionalOnProperty(name = "swagger.enabled", havingValue = "true")
@Profile({"local", "dev"}) // Only active in development environments
@Order(2) // Execute after main JWT authentication filter
public class SwaggerJwtCookieFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(SwaggerJwtCookieFilter.class);

    @Value("${swagger.jwt-cookie-name:jwt}")
    private String jwtCookieName;

    @Value("${swagger.jwt:jwt}")
    private String jwt;

    @Autowired
    private JWTCookieUtil cookieUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Handle Swagger UI JWT cookie synchronization
        handleSwaggerJwtCookieSync(request, response);

        filterChain.doFilter(request, response);
    }

    /**
     * Handle JWT cookie synchronization for Swagger UI
     * This method syncs JWT from Authorization header to cookie for development purposes
     */
    private void handleSwaggerJwtCookieSync(HttpServletRequest request, HttpServletResponse response) {
        try {
            // Only process requests with Authorization header (from Swagger UI)
            String bearerToken = request.getHeader("Authorization");
            if (!StringUtils.hasText(bearerToken) || !bearerToken.startsWith("Bearer ")) {
                return; // No Authorization header, skip sync
            }

            String headerToken = bearerToken.substring(7);
            if (!StringUtils.hasText(headerToken)) {
                return; // Empty token, skip sync
            }

            // Only sync if response is not committed (to avoid IllegalStateException)
            if (response.isCommitted()) {
                log.debug("Response already committed, cannot set JWT cookie");
                return;
            }

            // Check if current cookie has different token
            String currentJwtCookie = getCurrentJwtCookie(request);

            // Sync if cookie is different from header token
            if (!headerToken.equals(currentJwtCookie)) {
                // Set JWT cookie for Swagger UI compatibility
                Cookie jwtCookie = new Cookie(jwtCookieName, headerToken);
                jwtCookie.setPath("/");
                jwtCookie.setMaxAge(7200); // 2 hours
                jwtCookie.setHttpOnly(false); // Allow JavaScript access for Swagger UI
                jwtCookie.setSecure(request.isSecure());
                response.addCookie(jwtCookie);

                log.info("Swagger JWT cookie synced from Authorization header for URI: {}", request.getRequestURI());
            }
        } catch (Exception e) {
            log.debug("Could not sync Swagger JWT cookie: {}", e.getMessage());
        }
    }

    /**
     * Get current JWT cookie value
     */
    private String getCurrentJwtCookie(HttpServletRequest request) {
        if (request.getCookies() != null) {
            for (Cookie cookie : request.getCookies()) {
                if (jwtCookieName.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String uri = request.getRequestURI();
        // Only apply to Swagger UI related requests and API endpoints
        return !(uri.contains("/swagger-ui") ||
                uri.contains("/v3/api-docs") ||
                uri.startsWith("/VMS/admission"));
    }
}