<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VMS Swagger JWT Manager</title>
    <!-- Include crypto-js for HMAC-SHA256 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .config-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .config-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            color: #856404;
        }
        .config-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .role-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .role-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .role-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .clear-btn {
            background-color: #e74c3c;
        }
        .clear-btn:hover {
            background-color: #c0392b;
        }
        .swagger-btn {
            background-color: #27ae60;
        }
        .swagger-btn:hover {
            background-color: #229954;
        }
        .copy-btn {
            background-color: #f39c12;
        }
        .copy-btn:hover {
            background-color: #e67e22;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .current-token {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f4f8;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
        .token-display {
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
        }
        .links {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 15px;
        }
        .links a:hover {
            text-decoration: underline;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 VMS Swagger JWT Manager</h1>

        <div class="warning">
            <strong>⚠️ 注意：</strong> 這個工具僅用於開發和測試環境。請確保你的 JWT secret 配置正確。
        </div>

        <div class="config-section">
            <div class="config-title">🔧 JWT 配置 (從你的 application.properties 複製)</div>
            <label>JWT Secret:</label>
            <input type="text" id="jwtSecret" class="config-input"
                   placeholder="從 application.properties 中的 jwt.secret 複製"
                   value="9bgAs07fmAahVzVJKS2sebqziHDHfceT">

            <label>JWT Issuer:</label>
            <input type="text" id="jwtIssuer" class="config-input"
                   placeholder="從 application.properties 中的 jwt.issuer 複製"
                   value="eduhk-vms">

            <label>過期時間 (毫秒):</label>
            <input type="number" id="jwtExpiration" class="config-input"
                   placeholder="Token 過期時間，例如：1800000 (30分鐘)"
                   value="1800000">
        </div>

        <div class="role-section">
            <div class="role-title">👤 APPLICANT</div>
            <div class="role-description">
                Regular user role for visa applicants. Can submit applications and upload documents.
                <br><strong>Permissions:</strong> VISA_APPLICATION, DOCUMENT_UPLOAD
                <br><strong>Default User:</strong> test_applicant_user
            </div>
            <button onclick="generateToken('APPLICANT')">Set APPLICANT Token</button>
            <button class="copy-btn" onclick="copyTokenToClipboard('APPLICANT')">📋 Copy Token</button>
        </div>

        <div class="role-section">
            <div class="role-title">👨‍💼 ADMIN</div>
            <div class="role-description">
                Administrator role for staff members. Can manage visa applications and perform validations.
                <br><strong>Permissions:</strong> VISA_SETUP, VISA_VALIDATION
                <br><strong>Default User:</strong> test_admin_user
            </div>
            <button onclick="generateToken('ADMIN')">Set ADMIN Token</button>
            <button class="copy-btn" onclick="copyTokenToClipboard('ADMIN')">📋 Copy Token</button>
        </div>

        <div class="role-section">
            <div class="role-title">👑 SUPER_ADMIN</div>
            <div class="role-description">
                Super administrator role with full system access. Can manage users and system configurations.
                <br><strong>Permissions:</strong> VISA_SETUP, VISA_VALIDATION, USER_MANAGEMENT, SYSTEM_CONFIG
                <br><strong>Default User:</strong> test_super_admin_user
            </div>
            <button onclick="generateToken('SUPER_ADMIN')">Set SUPER_ADMIN Token</button>
            <button class="copy-btn" onclick="copyTokenToClipboard('SUPER_ADMIN')">📋 Copy Token</button>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="clear-btn" onclick="clearToken()">🗑️ Clear JWT Cookie</button>
            <button class="swagger-btn" onclick="openSwagger()">📖 Open Swagger UI</button>
        </div>

        <div id="status" class="status"></div>

        <div class="current-token">
            <strong>Current JWT Cookie:</strong>
            <div id="currentToken" class="token-display">No token set</div>
            <button onclick="refreshCurrentToken()" style="margin-top: 10px;">🔄 Refresh</button>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="syncFromSwaggerUI()" style="background-color: #6f42c1;">🔄 Sync JWT from Swagger UI</button>
            <button onclick="monitorSwaggerUI()" id="monitorBtn" style="background-color: #fd7e14;">👁️ Start Monitoring Swagger UI</button>
            <button onclick="injectSwaggerUISync()" style="background-color: #28a745;">💉 Inject Auto-Sync to Swagger UI</button>
            <button onclick="debugSwaggerAuth()" style="background-color: #dc3545;">🐛 Debug Swagger Auth</button>
            <button onclick="enableSwaggerAutoSync()" style="background-color: #17a2b8;">🚀 Enable Swagger Auto-Sync</button>
        </div>

        <div class="links">
            <a href="/VMS/swagger-ui/index.html" target="_blank">📖 Swagger UI</a>
            <a href="/v3/api-docs" target="_blank">📄 OpenAPI JSON</a>
        </div>
    </div>

    <script>
        // JWT implementation using crypto-js for proper HMAC-SHA256 signing
        function base64UrlEncode(str) {
            return btoa(str)
                .replace(/\+/g, '-')
                .replace(/\//g, '_')
                .replace(/=/g, '');
        }

        function base64UrlEncodeFromWordArray(wordArray) {
            const base64 = CryptoJS.enc.Base64.stringify(wordArray);
            return base64
                .replace(/\+/g, '-')
                .replace(/\//g, '_')
                .replace(/=/g, '');
        }

        function createJWT(payload, secret) {
            const header = {
                "alg": "HS256",
                "typ": "JWT"
            };

            const encodedHeader = base64UrlEncode(JSON.stringify(header));
            const encodedPayload = base64UrlEncode(JSON.stringify(payload));

            // Create the signing input
            const signingInput = `${encodedHeader}.${encodedPayload}`;

            // Generate HMAC-SHA256 signature
            const hash = CryptoJS.HmacSHA256(signingInput, secret);
            const signature = base64UrlEncodeFromWordArray(hash);

            return `${encodedHeader}.${encodedPayload}.${signature}`;
        }

        // Show status message
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (isError ? 'error' : 'success');
            status.style.display = 'block';

            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        // Get role-specific user ID and permissions
        function getRoleConfig(role) {
            const configs = {
                'APPLICANT': {
                    userId: 'test_applicant_user',
                    permissions: ['VISA_APPLICATION', 'DOCUMENT_UPLOAD']
                },
                'ADMIN': {
                    userId: 'test_admin_user',
                    permissions: ['VISA_SETUP', 'VISA_VALIDATION']
                },
                'SUPER_ADMIN': {
                    userId: 'test_super_admin_user',
                    permissions: ['VISA_SETUP', 'VISA_VALIDATION', 'USER_MANAGEMENT', 'SYSTEM_CONFIG']
                }
            };
            return configs[role] || configs['APPLICANT'];
        }

        // Generate JWT token for specific role (client-side)
        function generateToken(role) {
            try {
                const secret = document.getElementById('jwtSecret').value;
                const issuer = document.getElementById('jwtIssuer').value;
                const expiration = parseInt(document.getElementById('jwtExpiration').value);

                if (!secret || !issuer) {
                    showStatus('❌ 請填寫 JWT Secret 和 Issuer', true);
                    return;
                }

                const roleConfig = getRoleConfig(role);
                const now = Math.floor(Date.now() / 1000);
                const exp = now + Math.floor(expiration / 1000);

                const payload = {
                    sub: roleConfig.userId,
                    userId: roleConfig.userId,
                    type: "access",
                    iss: issuer,
                    iat: now,
                    exp: exp,
                    auth: role,
                    role: role,
                    permissions: roleConfig.permissions
                };

                const token = createJWT(payload, secret);

                // Set cookie
                document.cookie = `jwt=${token}; path=/; max-age=${Math.floor(expiration / 1000)}`;

                showStatus(`✅ ${role} token 生成成功！User: ${roleConfig.userId}`);
                refreshCurrentToken();

            } catch (error) {
                showStatus(`❌ 生成 token 時發生錯誤: ${error.message}`, true);
            }
        }

        // Copy token to clipboard
        function copyTokenToClipboard(role) {
            try {
                const secret = document.getElementById('jwtSecret').value;
                const issuer = document.getElementById('jwtIssuer').value;
                const expiration = parseInt(document.getElementById('jwtExpiration').value);

                if (!secret || !issuer) {
                    showStatus('❌ 請填寫 JWT Secret 和 Issuer', true);
                    return;
                }

                const roleConfig = getRoleConfig(role);
                const now = Math.floor(Date.now() / 1000);
                const exp = now + Math.floor(expiration / 1000);

                const payload = {
                    sub: roleConfig.userId,
                    userId: roleConfig.userId,
                    type: "access",
                    iss: issuer,
                    iat: now,
                    exp: exp,
                    auth: role,
                    role: role,
                    permissions: roleConfig.permissions
                };

                const token = createJWT(payload, secret);

                // Copy to clipboard
                navigator.clipboard.writeText(token).then(() => {
                    showStatus(`✅ ${role} token 已複製到剪貼簿！`);
                }).catch(() => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = token;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showStatus(`✅ ${role} token 已複製到剪貼簿！`);
                });

            } catch (error) {
                showStatus(`❌ 複製 token 時發生錯誤: ${error.message}`, true);
            }
        }

        // Clear JWT cookie
        function clearToken() {
            document.cookie = 'jwt=; path=/; max-age=0';
            showStatus('✅ JWT cookie 已清除！');
            refreshCurrentToken();
        }

        // Get current JWT cookie value
        function getCurrentJwtCookie() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'jwt') {
                    return value;
                }
            }
            return null;
        }

        // Decode base64url
        function base64UrlDecode(str) {
            // Add padding if needed
            str += '='.repeat((4 - str.length % 4) % 4);
            // Replace URL-safe characters
            str = str.replace(/-/g, '+').replace(/_/g, '/');
            return atob(str);
        }

        // Refresh current token display
        function refreshCurrentToken() {
            const currentToken = getCurrentJwtCookie();
            const tokenDisplay = document.getElementById('currentToken');

            if (currentToken && currentToken !== 'jwt' && currentToken.includes('.')) {
                // Decode JWT payload to show user info
                try {
                    const parts = currentToken.split('.');
                    if (parts.length === 3) {
                        const payload = JSON.parse(base64UrlDecode(parts[1]));
                        const isExpired = payload.exp && (payload.exp * 1000) < Date.now();

                        tokenDisplay.innerHTML = `
                            <strong>User:</strong> ${payload.userId || payload.sub}<br>
                            <strong>Role:</strong> ${payload.role || payload.auth}<br>
                            <strong>Permissions:</strong> ${(payload.permissions || []).join(', ')}<br>
                            <strong>Issued:</strong> ${payload.iat ? new Date(payload.iat * 1000).toLocaleString() : 'N/A'}<br>
                            <strong>Expires:</strong> ${payload.exp ? new Date(payload.exp * 1000).toLocaleString() : 'N/A'} ${isExpired ? '<span style="color: red;">(已過期)</span>' : '<span style="color: green;">(有效)</span>'}<br>
                            <strong>Token:</strong> ${currentToken.substring(0, 50)}...
                        `;
                    } else {
                        tokenDisplay.textContent = '無效的 JWT 格式';
                    }
                } catch (e) {
                    tokenDisplay.innerHTML = `
                        <strong>Token:</strong> ${currentToken.substring(0, 50)}...<br>
                        <span style="color: red;">無法解析 token: ${e.message}</span>
                    `;
                }
            } else {
                tokenDisplay.textContent = 'No token set';
            }
        }

        // Open Swagger UI
        function openSwagger() {
            window.open('/VMS/swagger-ui/index.html', '_blank');
        }

        // Load configuration from localStorage
        function loadConfig() {
            const savedSecret = localStorage.getItem('vms-jwt-secret');
            const savedIssuer = localStorage.getItem('vms-jwt-issuer');
            const savedExpiration = localStorage.getItem('vms-jwt-expiration');

            if (savedSecret) document.getElementById('jwtSecret').value = savedSecret;
            if (savedIssuer) document.getElementById('jwtIssuer').value = savedIssuer;
            if (savedExpiration) document.getElementById('jwtExpiration').value = savedExpiration;
        }

        // Save configuration to localStorage
        function saveConfig() {
            localStorage.setItem('vms-jwt-secret', document.getElementById('jwtSecret').value);
            localStorage.setItem('vms-jwt-issuer', document.getElementById('jwtIssuer').value);
            localStorage.setItem('vms-jwt-expiration', document.getElementById('jwtExpiration').value);
        }

        // Add event listeners for config inputs
        function setupConfigListeners() {
            document.getElementById('jwtSecret').addEventListener('change', saveConfig);
            document.getElementById('jwtIssuer').addEventListener('change', saveConfig);
            document.getElementById('jwtExpiration').addEventListener('change', saveConfig);
        }

        // Sync JWT from Swagger UI
        function syncFromSwaggerUI() {
            try {
                // Try to get JWT from Swagger UI's localStorage
                const swaggerAuth = localStorage.getItem('swagger-ui-auth');
                if (swaggerAuth) {
                    const authData = JSON.parse(swaggerAuth);
                    if (authData.bearerAuth && authData.bearerAuth.value) {
                        const token = authData.bearerAuth.value;
                        document.cookie = `jwt=${token}; path=/; max-age=1800`;
                        showStatus('✅ JWT synchronized from Swagger UI!');
                        refreshCurrentToken();
                        return;
                    }
                }

                // Try alternative method - check if Swagger UI window is available
                const swaggerWindow = window.open('/VMS/swagger-ui/index.html', 'swagger', 'width=1,height=1');
                if (swaggerWindow) {
                    setTimeout(() => {
                        try {
                            const swaggerAuth = swaggerWindow.localStorage.getItem('swagger-ui-auth');
                            if (swaggerAuth) {
                                const authData = JSON.parse(swaggerAuth);
                                if (authData.bearerAuth && authData.bearerAuth.value) {
                                    const token = authData.bearerAuth.value;
                                    document.cookie = `jwt=${token}; path=/; max-age=1800`;
                                    showStatus('✅ JWT synchronized from Swagger UI!');
                                    refreshCurrentToken();
                                }
                            }
                            swaggerWindow.close();
                        } catch (e) {
                            swaggerWindow.close();
                            showStatus('❌ Could not access Swagger UI data. Please copy JWT manually.', true);
                        }
                    }, 1000);
                } else {
                    showStatus('❌ Could not access Swagger UI. Please copy JWT manually.', true);
                }
            } catch (error) {
                showStatus('❌ Error syncing from Swagger UI: ' + error.message, true);
            }
        }

        // Monitor Swagger UI for JWT changes
        let monitoringInterval = null;
        function monitorSwaggerUI() {
            const btn = document.getElementById('monitorBtn');

            if (monitoringInterval) {
                // Stop monitoring
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                btn.textContent = '👁️ Start Monitoring Swagger UI';
                btn.style.backgroundColor = '#fd7e14';
                showStatus('🛑 Stopped monitoring Swagger UI');
            } else {
                // Start monitoring
                monitoringInterval = setInterval(() => {
                    try {
                        const swaggerAuth = localStorage.getItem('swagger-ui-auth');
                        if (swaggerAuth) {
                            const authData = JSON.parse(swaggerAuth);
                            if (authData.bearerAuth && authData.bearerAuth.value) {
                                const token = authData.bearerAuth.value;
                                const currentCookie = getCurrentJwtCookie();

                                if (token !== currentCookie) {
                                    document.cookie = `jwt=${token}; path=/; max-age=1800`;
                                    showStatus('🔄 JWT auto-synchronized from Swagger UI!');
                                    refreshCurrentToken();
                                }
                            }
                        }
                    } catch (e) {
                        // Ignore errors during monitoring
                    }
                }, 2000); // Check every 2 seconds

                btn.textContent = '🛑 Stop Monitoring';
                btn.style.backgroundColor = '#dc3545';
                showStatus('👁️ Started monitoring Swagger UI for JWT changes');
            }
        }

        // Inject JWT sync script into Swagger UI
        function injectSwaggerUISync() {
            try {
                // Try to inject the sync script into any open Swagger UI windows
                const swaggerWindows = [];

                // Check if Swagger UI is open in another tab/window
                if (window.opener && window.opener.location.href.includes('swagger-ui')) {
                    swaggerWindows.push(window.opener);
                }

                // Also try to open Swagger UI and inject script
                const swaggerWindow = window.open('/VMS/swagger-ui/index.html', 'swagger-ui');
                if (swaggerWindow) {
                    swaggerWindows.push(swaggerWindow);

                    // Wait for Swagger UI to load, then inject our sync script
                    setTimeout(() => {
                        try {
                            const script = swaggerWindow.document.createElement('script');
                            script.src = '/js/swagger-jwt-sync.js';
                            swaggerWindow.document.head.appendChild(script);

                            // Also inject inline script for immediate sync
                            const inlineScript = swaggerWindow.document.createElement('script');
                            inlineScript.textContent = `
                                // Immediate JWT sync function
                                function syncJWTFromAuth() {
                                    const authData = localStorage.getItem('swagger-ui-auth');
                                    if (authData) {
                                        try {
                                            const parsed = JSON.parse(authData);
                                            if (parsed.bearerAuth && parsed.bearerAuth.value) {
                                                document.cookie = 'jwt=' + parsed.bearerAuth.value + '; path=/; max-age=1800';
                                                console.log('JWT synced to cookie from Swagger UI');
                                            }
                                        } catch (e) {}
                                    }
                                }

                                // Monitor for auth changes
                                setInterval(syncJWTFromAuth, 2000);

                                // Listen for storage changes
                                window.addEventListener('storage', function(e) {
                                    if (e.key === 'swagger-ui-auth') {
                                        setTimeout(syncJWTFromAuth, 500);
                                    }
                                });
                            `;
                            swaggerWindow.document.head.appendChild(inlineScript);

                            showStatus('✅ JWT sync injected into Swagger UI!');
                        } catch (e) {
                            console.log('Could not inject script into Swagger UI:', e);
                        }
                    }, 3000);
                }
            } catch (error) {
                console.log('Could not inject Swagger UI sync:', error);
            }
        }

        // Debug Swagger Auth
        function debugSwaggerAuth() {
            console.log('=== Swagger Auth Debug ===');

            // Check localStorage
            console.log('localStorage contents:');
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                console.log(`  ${key}:`, value);
            }

            // Check sessionStorage
            console.log('sessionStorage contents:');
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                const value = sessionStorage.getItem(key);
                console.log(`  ${key}:`, value);
            }

            // Check cookies
            console.log('Current cookies:');
            document.cookie.split(';').forEach(cookie => {
                console.log(`  ${cookie.trim()}`);
            });

            // Try to manually sync
            const possibleKeys = ['swagger-ui-auth', 'swagger-ui:auth', 'swaggerUIAuth', 'auth'];
            let foundToken = null;

            for (let key of possibleKeys) {
                const authData = localStorage.getItem(key);
                if (authData) {
                    try {
                        const parsed = JSON.parse(authData);
                        if (parsed.bearerAuth && parsed.bearerAuth.value) {
                            foundToken = parsed.bearerAuth.value;
                            console.log(`Found JWT in localStorage[${key}]:`, foundToken.substring(0, 50) + '...');
                            break;
                        }
                    } catch (e) {
                        console.log(`Could not parse localStorage[${key}]:`, e);
                    }
                }
            }

            if (foundToken) {
                document.cookie = `jwt=${foundToken}; path=/; max-age=1800`;
                console.log('✅ Manually set JWT cookie');
                showStatus('✅ Found and set JWT token from Swagger UI!');
                refreshCurrentToken();
            } else {
                console.log('❌ No JWT token found in any storage');
                showStatus('❌ No JWT token found in Swagger UI storage', true);
            }
        }

        // Enable Swagger Auto-Sync
        function enableSwaggerAutoSync() {
            // Open Swagger UI in a new window/tab
            const swaggerWindow = window.open('/VMS/swagger-ui/index.html', 'swagger-ui');

            if (swaggerWindow) {
                showStatus('🚀 Opening Swagger UI with auto-sync...');

                // Wait for Swagger UI to load, then inject sync script
                setTimeout(() => {
                    try {
                        // Inject comprehensive sync script
                        const script = swaggerWindow.document.createElement('script');
                        script.textContent = `
                            (function() {
                                console.log('🚀 JWT Auto-Sync Enabled!');

                                let lastToken = null;

                                function syncJWTToCookie() {
                                    try {
                                        // Method 1: Check Swagger UI internal state
                                        if (window.ui && window.ui.authSelectors) {
                                            const auth = window.ui.authSelectors.authorized();
                                            if (auth && auth.get && auth.get('bearerAuth'))
                                                const bearerAuth = auth.get('bearerAuth');
                                                if (bearerAuth && bearerAuth.get && bearerAuth.get('value')) {
                                                    const token = bearerAuth.get('value');
                                                    if (token && token !== lastToken && token.length > 50) {
                                                        lastToken = token;
                                                        document.cookie = 'jwt=' + token + '; path=/; max-age=7200';
                                                        console.log('✅ JWT synced to cookie:', token.substring(0, 50) + '...');

                                                        // Show notification
                                                        const notification = document.createElement('div');
                                                        notification.style.cssText = 'position:fixed;top:20px;right:20px;background:#28a745;color:white;padding:10px;border-radius:4px;z-index:10000;';
                                                        notification.textContent = '✅ JWT synced to cookie!';
                                                        document.body.appendChild(notification);
                                                        setTimeout(() => notification.remove(), 3000);

                                                        return true;
                                                    }
                                                }
                                            }
                                        }

                                        // Method 2: Check localStorage
                                        const authData = localStorage.getItem('swagger-ui-auth');
                                        if (authData) {
                                            const parsed = JSON.parse(authData);
                                            if (parsed.bearerAuth && parsed.bearerAuth.value) {
                                                const token = parsed.bearerAuth.value;
                                                if (token && token !== lastToken && token.length > 50) {
                                                    lastToken = token;
                                                    document.cookie = 'jwt=' + token + '; path=/; max-age=7200';
                                                    console.log('✅ JWT synced from localStorage:', token.substring(0, 50) + '...');
                                                    return true;
                                                }
                                            }
                                        }
                                    } catch (e) {
                                        console.debug('JWT sync error:', e);
                                    }
                                    return false;
                                }

                                // Monitor for changes
                                setInterval(syncJWTToCookie, 2000);

                                // Listen for authorize button clicks
                                document.addEventListener('click', function(e) {
                                    if (e.target.textContent && e.target.textContent.includes('Authorize')) {
                                        console.log('🔍 Authorize clicked, checking for JWT...');
                                        setTimeout(syncJWTToCookie, 1000);
                                        setTimeout(syncJWTToCookie, 3000);
                                    }
                                });

                                // Listen for storage changes
                                window.addEventListener('storage', function(e) {
                                    if (e.key === 'swagger-ui-auth') {
                                        setTimeout(syncJWTToCookie, 500);
                                    }
                                });

                                // Initial sync
                                setTimeout(syncJWTToCookie, 3000);

                                // Expose for manual testing
                                window.jwtSync = { sync: syncJWTToCookie };
                            })();
                        `;
                        swaggerWindow.document.head.appendChild(script);

                        showStatus('✅ Auto-sync script injected into Swagger UI!');
                    } catch (e) {
                        showStatus('❌ Could not inject script: ' + e.message, true);
                    }
                }, 5000); // Wait 5 seconds for Swagger UI to fully load
            } else {
                showStatus('❌ Could not open Swagger UI window', true);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            setupConfigListeners();
            refreshCurrentToken();

            // Auto-refresh token display every 5 seconds
            setInterval(refreshCurrentToken, 5000);

            // Inject sync script into Swagger UI
            setTimeout(injectSwaggerUISync, 1000);
        });
    </script>
</body>
</html>
