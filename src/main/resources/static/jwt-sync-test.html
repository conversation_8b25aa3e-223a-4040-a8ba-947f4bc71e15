<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Cookie Sync Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .success {
            background-color: #27ae60;
        }
        .warning {
            background-color: #f39c12;
        }
        .danger {
            background-color: #e74c3c;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #ecf0f1;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .links {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 15px;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JWT Cookie 同步測試工具</h1>
        
        <div class="test-section">
            <div class="test-title">📋 當前狀態檢查</div>
            <button onclick="checkCurrentStatus()">檢查當前狀態</button>
            <button onclick="checkSwaggerUIStatus()">檢查 Swagger UI 狀態</button>
            <div id="statusResult" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🍪 Cookie 操作測試</div>
            <button onclick="testSetCookie()">設置測試 Cookie</button>
            <button onclick="testGetCookie()">讀取 JWT Cookie</button>
            <button onclick="testClearCookie()" class="danger">清除 JWT Cookie</button>
            <div id="cookieResult" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔄 同步功能測試</div>
            <button onclick="testManualSync()">手動同步測試</button>
            <button onclick="testAutoSync()">自動同步測試</button>
            <button onclick="simulateSwaggerAuth()">模擬 Swagger 認證</button>
            <div id="syncResult" class="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🐛 調試工具</div>
            <button onclick="debugLocalStorage()">檢查 localStorage</button>
            <button onclick="debugSwaggerUI()">調試 Swagger UI</button>
            <button onclick="debugCookies()">調試所有 Cookies</button>
            <div id="debugResult" class="result"></div>
        </div>

        <div id="globalStatus" class="status"></div>

        <div class="links">
            <a href="/VMS/swagger-ui/index.html" target="_blank">📖 Swagger UI</a>
            <a href="/VMS/swagger-jwt-manager.html" target="_blank">🔐 JWT Manager</a>
        </div>
    </div>

    <script>
        // Configuration
        const JWT_COOKIE_NAME = 'jwt';
        const COOKIE_PATH = '/VMS';
        const COOKIE_MAX_AGE = 7200;

        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
        }

        function showStatus(message, type = 'info') {
            const element = document.getElementById('globalStatus');
            element.innerHTML = message;
            element.className = `status ${type}`;
            setTimeout(() => {
                element.innerHTML = '';
                element.className = 'status';
            }, 5000);
        }

        // Cookie utilities
        function setJwtCookie(token) {
            document.cookie = `${JWT_COOKIE_NAME}=${token}; path=${COOKIE_PATH}; max-age=${COOKIE_MAX_AGE}; SameSite=Lax`;
        }

        function getJwtCookie() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === JWT_COOKIE_NAME) {
                    return value;
                }
            }
            return null;
        }

        function clearJwtCookie() {
            document.cookie = `${JWT_COOKIE_NAME}=; path=${COOKIE_PATH}; max-age=0`;
        }

        // Test functions
        function checkCurrentStatus() {
            const cookie = getJwtCookie();
            const localStorage = window.localStorage.getItem('swagger-ui-auth');
            
            let result = `<strong>JWT Cookie:</strong> ${cookie ? cookie.substring(0, 50) + '...' : 'Not found'}<br>`;
            result += `<strong>localStorage:</strong> ${localStorage ? 'Found' : 'Not found'}<br>`;
            result += `<strong>Swagger UI:</strong> ${window.ui ? 'Loaded' : 'Not loaded'}<br>`;
            result += `<strong>Current Path:</strong> ${window.location.pathname}<br>`;
            result += `<strong>Cookie Path:</strong> ${COOKIE_PATH}`;
            
            showResult('statusResult', result);
            showStatus('✅ 狀態檢查完成', 'success');
        }

        function checkSwaggerUIStatus() {
            let result = '';
            
            if (window.ui && window.ui.authSelectors) {
                try {
                    const auth = window.ui.authSelectors.authorized();
                    if (auth && auth.get) {
                        const bearerAuth = auth.get('bearerAuth');
                        if (bearerAuth && bearerAuth.get) {
                            const token = bearerAuth.get('value');
                            result = `<strong>Swagger UI Auth:</strong> ${token ? token.substring(0, 50) + '...' : 'No token'}<br>`;
                        } else {
                            result = '<strong>Swagger UI Auth:</strong> No bearerAuth found<br>';
                        }
                    } else {
                        result = '<strong>Swagger UI Auth:</strong> No auth state found<br>';
                    }
                } catch (e) {
                    result = `<strong>Swagger UI Auth Error:</strong> ${e.message}<br>`;
                }
            } else {
                result = '<strong>Swagger UI:</strong> Not available or not loaded<br>';
            }
            
            // Check if sync functions are available
            result += `<strong>jwtSync API:</strong> ${window.jwtSync ? 'Available' : 'Not available'}<br>`;
            result += `<strong>swaggerJwtSync API:</strong> ${window.swaggerJwtSync ? 'Available' : 'Not available'}`;
            
            showResult('statusResult', result);
        }

        function testSetCookie() {
            const testToken = 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0X3VzZXIiLCJ1c2VySWQiOiJ0ZXN0X3VzZXIiLCJ0eXBlIjoiYWNjZXNzIiwiaXNzIjoiZWR1aGstdm1zIiwiaWF0IjoxNzM3MTg0ODAwLCJleHAiOjE3MzcxOTIwMDAsImF1dGgiOiJBRE1JTiIsInJvbGUiOiJBRE1JTiJ9.test';
            setJwtCookie(testToken);
            
            const result = `<strong>測試 Token 已設置:</strong><br>${testToken}`;
            showResult('cookieResult', result);
            showStatus('✅ 測試 Cookie 已設置', 'success');
        }

        function testGetCookie() {
            const cookie = getJwtCookie();
            const result = cookie ? 
                `<strong>JWT Cookie 值:</strong><br>${cookie}` : 
                '<strong>未找到 JWT Cookie</strong>';
            
            showResult('cookieResult', result);
            showStatus(cookie ? '✅ 找到 JWT Cookie' : '❌ 未找到 JWT Cookie', cookie ? 'success' : 'warning');
        }

        function testClearCookie() {
            clearJwtCookie();
            showResult('cookieResult', '<strong>JWT Cookie 已清除</strong>');
            showStatus('✅ JWT Cookie 已清除', 'success');
        }

        function testManualSync() {
            if (window.jwtSync && window.jwtSync.sync) {
                window.jwtSync.sync();
                showResult('syncResult', '<strong>手動同步已執行</strong>');
                showStatus('✅ 手動同步完成', 'success');
            } else if (window.swaggerJwtSync && window.swaggerJwtSync.sync) {
                window.swaggerJwtSync.sync();
                showResult('syncResult', '<strong>手動同步已執行 (舊版)</strong>');
                showStatus('✅ 手動同步完成', 'success');
            } else {
                showResult('syncResult', '<strong>同步功能不可用</strong>');
                showStatus('❌ 同步功能不可用', 'danger');
            }
        }

        function testAutoSync() {
            // Simulate auto sync by checking current state
            const cookie = getJwtCookie();
            const swaggerToken = window.jwtSync ? window.jwtSync.getCurrentToken() : null;
            
            let result = `<strong>當前 Cookie:</strong> ${cookie ? cookie.substring(0, 30) + '...' : 'None'}<br>`;
            result += `<strong>Swagger Token:</strong> ${swaggerToken ? swaggerToken.substring(0, 30) + '...' : 'None'}<br>`;
            result += `<strong>同步狀態:</strong> ${cookie === swaggerToken ? '已同步' : '未同步'}`;
            
            showResult('syncResult', result);
        }

        function simulateSwaggerAuth() {
            // Simulate setting auth in localStorage
            const testAuth = {
                bearerAuth: {
                    value: 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzaW11bGF0ZWRfdXNlciIsInJvbGUiOiJBRE1JTiJ9.simulated'
                }
            };
            
            localStorage.setItem('swagger-ui-auth', JSON.stringify(testAuth));
            showResult('syncResult', '<strong>模擬 Swagger 認證已設置</strong><br>請測試自動同步功能');
            showStatus('✅ 模擬認證已設置', 'success');
        }

        function debugLocalStorage() {
            let result = '<strong>localStorage 內容:</strong><br>';
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                result += `${key}: ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}<br>`;
            }
            showResult('debugResult', result);
        }

        function debugSwaggerUI() {
            let result = '<strong>Swagger UI 調試信息:</strong><br>';
            result += `window.ui: ${window.ui ? 'Available' : 'Not available'}<br>`;
            
            if (window.ui) {
                result += `authSelectors: ${window.ui.authSelectors ? 'Available' : 'Not available'}<br>`;
                if (window.ui.authSelectors) {
                    try {
                        const auth = window.ui.authSelectors.authorized();
                        result += `authorized(): ${auth ? 'Available' : 'Not available'}<br>`;
                    } catch (e) {
                        result += `authorized() error: ${e.message}<br>`;
                    }
                }
            }
            
            showResult('debugResult', result);
        }

        function debugCookies() {
            const cookies = document.cookie.split(';');
            let result = '<strong>所有 Cookies:</strong><br>';
            
            if (cookies.length === 1 && cookies[0] === '') {
                result += '無 Cookies';
            } else {
                cookies.forEach(cookie => {
                    const [name, value] = cookie.trim().split('=');
                    result += `${name}: ${value ? value.substring(0, 50) + (value.length > 50 ? '...' : '') : 'empty'}<br>`;
                });
            }
            
            showResult('debugResult', result);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('🚀 JWT Cookie 同步測試工具已載入', 'success');
            checkCurrentStatus();
        });
    </script>
</body>
</html>
