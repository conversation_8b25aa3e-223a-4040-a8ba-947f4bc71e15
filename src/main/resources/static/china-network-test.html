<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>China Network Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🇨🇳 China Network Connectivity Test</h1>
    <p>This page helps test network connectivity and performance for users in China.</p>

    <div class="test-section">
        <h3>1. Basic Connectivity Test</h3>
        <button onclick="testBasicConnectivity()">Test Basic Connection</button>
        <div id="connectivity-result"></div>
    </div>

    <div class="test-section">
        <h3>2. API Response Time Test</h3>
        <button onclick="testAPIResponseTime()">Test API Speed</button>
        <div id="api-speed-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Login Simulation Test</h3>
        <button onclick="testLoginSimulation()">Test Login Process</button>
        <div id="login-test-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Network Information</h3>
        <button onclick="showNetworkInfo()">Show Network Info</button>
        <div id="network-info-result"></div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function testBasicConnectivity() {
            showResult('connectivity-result', '🔄 Testing basic connectivity...', 'info');
            
            const startTime = Date.now();
            fetch('/v3/api-docs')
                .then(response => {
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    
                    if (response.ok) {
                        showResult('connectivity-result', 
                            `✅ Connection successful!<br>
                             Response time: ${responseTime}ms<br>
                             Status: ${response.status}`, 'success');
                    } else {
                        showResult('connectivity-result', 
                            `⚠️ Connection issues detected<br>
                             Status: ${response.status}<br>
                             Response time: ${responseTime}ms`, 'error');
                    }
                })
                .catch(error => {
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    showResult('connectivity-result', 
                        `❌ Connection failed<br>
                         Error: ${error.message}<br>
                         Time elapsed: ${responseTime}ms`, 'error');
                });
        }

        function testAPIResponseTime() {
            showResult('api-speed-result', '🔄 Testing API response times...', 'info');
            
            const tests = [
                { name: 'API Docs', url: '/v3/api-docs' },
                { name: 'Swagger UI', url: '/swagger-ui/index.html' }
            ];
            
            let results = [];
            let completed = 0;
            
            tests.forEach(test => {
                const startTime = Date.now();
                fetch(test.url)
                    .then(response => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        results.push(`${test.name}: ${responseTime}ms (${response.status})`);
                        completed++;
                        
                        if (completed === tests.length) {
                            showResult('api-speed-result', 
                                `📊 API Response Times:<br>${results.join('<br>')}`, 
                                'success');
                        }
                    })
                    .catch(error => {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;
                        results.push(`${test.name}: Failed (${responseTime}ms) - ${error.message}`);
                        completed++;
                        
                        if (completed === tests.length) {
                            showResult('api-speed-result', 
                                `📊 API Response Times:<br>${results.join('<br>')}`, 
                                'error');
                        }
                    });
            });
        }

        function testLoginSimulation() {
            showResult('login-test-result', '🔄 Simulating login process...', 'info');
            
            // 模擬登錄流程的各個步驟
            const steps = [
                { name: 'Load login page', delay: 100 },
                { name: 'Submit credentials', delay: 200 },
                { name: 'Validate session', delay: 150 },
                { name: 'Load dashboard', delay: 300 }
            ];
            
            let currentStep = 0;
            let totalTime = 0;
            
            function runNextStep() {
                if (currentStep >= steps.length) {
                    showResult('login-test-result', 
                        `✅ Login simulation completed<br>
                         Total time: ${totalTime}ms<br>
                         Average step time: ${Math.round(totalTime / steps.length)}ms`, 
                        'success');
                    return;
                }
                
                const step = steps[currentStep];
                const startTime = Date.now();
                
                setTimeout(() => {
                    const stepTime = Date.now() - startTime;
                    totalTime += stepTime;
                    currentStep++;
                    
                    showResult('login-test-result', 
                        `🔄 Step ${currentStep}/${steps.length}: ${step.name} (${stepTime}ms)`, 
                        'info');
                    
                    runNextStep();
                }, step.delay);
            }
            
            runNextStep();
        }

        function showNetworkInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Language': navigator.language,
                'Platform': navigator.platform,
                'Online': navigator.onLine,
                'Connection': navigator.connection ? 
                    `${navigator.connection.effectiveType} (${navigator.connection.downlink}Mbps)` : 
                    'Unknown',
                'Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
                'Screen': `${screen.width}x${screen.height}`,
                'Current Time': new Date().toLocaleString()
            };
            
            const infoHtml = Object.entries(info)
                .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                .join('<br>');
            
            showResult('network-info-result', `📱 Network Information:<br>${infoHtml}`, 'info');
        }

        // 自動運行基本連接測試
        window.onload = function() {
            setTimeout(testBasicConnectivity, 1000);
        };
    </script>
</body>
</html>
