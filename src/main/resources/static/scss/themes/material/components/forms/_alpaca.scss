/* ------------------------------------------------------------------------------
*
*  # Alpaca forms
*
*  Styles for alpaca.min.js - the easiest way to generate interactive HTML5 forms for web applications
*
* ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-alpaca {

    // Added to outer field elements to hide them
    .alpaca-hidden {
        display: none;
    }

    // Helper text
    .alpaca-field {
        .help-block,
        .alpaca-field-text-max-length-indicator {
            color: $gray-600;
            margin-top: $form-text-margin-top;
            margin-bottom: $form-text-margin-top;
        }
    }

    // If input has error
    .has-error {

        // Override text color if menu has error
        .multiselect-container > .active .form-check {
            color: $white;
        }

        // Change background color in active items
        .btn-group.show .multiselect.btn,
        .multiselect.btn-light:active {
            color: theme-color('danger');
            border-color: theme-color('danger');
        }

    }

    // Disabled fields
    .alpaca-disabled {
        .form-check label,
        .form-check .switchery {
            cursor: $cursor-disabled;
            color: $gray-600;
        }
    }

    // Added to fields that have run through validation and are invalid
    .alpaca-invalid {

        // Text
        .alpaca-control-label,
        .alpaca-message {
            color: theme-color('danger');
        }

        // Input
        .form-control {
            color: theme-color('danger');

            // Double bottom border
            &:focus {
                box-shadow: 0 $input-border-width 0 theme-color('danger');
            }

            // Border color
            @include plain-hover-focus {
                border-color: theme-color('danger');
            }
        }
    }

    // General purpose HTML clear
    .alpaca-clear {
        clear: both;
    }

    // Right alignment
    .alpaca-float-right {
        float: right;
    }


    //
    // Icons
    //

    // Override glyphicon icons
    .alpaca-field {
        .glyphicon {
            font-family: $icon-font-family;
            font-size: $icon-font-size;
            vertical-align: middle;
            margin-top: ($font-size-base - $icon-font-size) / 2;
            font-style: normal;
            line-height: 1;
            display: inline-block;
            margin-right: $element-spacer-x;
        }

        // Info icon
        .glyphicon-info-sign {
            display: none;
        }

        // Error icon
        .glyphicon-exclamation-sign:before {
            content: $icon-validation-error;
        }
    }

    // Required asterisk
    .alpaca-icon-required {
        font-family: $font-family-base;
        float: right;
        margin-right: 0;
        margin-left: $element-spacer-x;

        // Required sign
        &:before {
            content: '*';
        }
    }


    //
    // Editor fields
    //

    .alpaca-controlfield-editor {
        position: relative;
        width: 100%;
        height: 300px;
        border: $input-border-width solid $input-border-color;

        // Element
        .control-field-editor-el {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0
        }
    }


    //
    // Ace editor
    //

    .ace_editor {
        border: $border-width solid rgb(204, 204, 204);
    }


    //
    // CKEditor
    //

    .alpaca-field-ckeditor.alpaca-invalid > .cke {
        border-color: $color-warning-800;
    }


    //
    // Option tree
    //

    .alpaca-field-optiontree {

        // Add left spacing to input field
        .optiontree + .form-control {
            margin-left: $element-spacer-x;
        }

        // Horizontal layout
        &.optiontree-horizontal {

            // Tree
            .optiontree {
                display: inline-block;

                // Selector
                .optiontree-selector {
                    display: inline-block;
                    margin-top: map-get($spacers, 1);
                    margin-bottom: map-get($spacers, 1);

                    // Hide empty container
                    &:empty {
                        display: none;
                    }

                    // Extra horizontal spacing
                    + .optiontree-selector {
                        margin-left: $element-spacer-x;
                    }
                }
            }

            // Input
             input {
                display: inline-block;
                width: auto;
            }

            // Label
             label {
                display: block;
            }
        }
    }


    //
    // Multiselect
    //

    // If error
    .has-error .multiselect {
        border-color: theme-color('danger');
        color: theme-color('danger');
    }


    //
    // Labels
    //

    .alpaca-container-label:not(legend) {
        margin-top: $spacer;
    }


    //
    // Toolbar
    //

    // Base
    .alpaca-array-toolbar {
        margin-bottom: ($spacer / 2);
    }

    // Action bar
    .alpaca-array-actionbar {
        overflow: hidden;

        // Top
        &.alpaca-array-actionbar-top {
            padding-bottom: ($spacer / 2);
        }

        // Bottom
        &.alpaca-array-actionbar-bottom {
            padding-top: ($spacer / 2);
        }

        // Stretch
        &,
        &.btn-group {
            width: 100%;
        }
    }

    // Field objects
    .alpaca-field-object,
    .alpaca-field-array {

        // Remove border
        .alpaca-top,
        .alpaca-top .alpaca-container {
            border: 0;
        }
    }
    .alpaca-container {
        border: 0;
    }


    //
    // Containers
    //

    // Add top spacing
    .alpaca-control-buttons-container {
        margin-top: ($spacer / 2);
    }

    // Remove border from field object
    .alpaca-container-item > .alpaca-container > .alpaca-field-object {
        border: 0;
    }

    // Buttons container in form
    .alpaca-form-buttons-container {
        margin-top: $spacer;
        text-align: right;
    }

    // Remove horizontal spacing from form group
    .alpaca-container > .form-group {
        margin-left: 0;
        margin-right: 0;
    }

    // Hidden field
    .alpaca-field-hidden {
        display: none;
    }

    // First item
    .alpaca-container-item:not(:first-child) {
        margin-top: ($spacer / 2);
    }

    // Last item
    .alpaca-container .alpaca-container-item:last-child {
        margin-bottom: 0;
    }

    // Select
    .alpaca-field-select .btn-group {
        width: 100%;
    }


    //
    // Fields
    //

    .alpaca-field {

        // Upload field
        &.alpaca-field-upload {

            // Active zone
            .fileupload-active-zone {
                margin-top: ($spacer * 2);
                margin-bottom: ($spacer * 2);
            }

            // Download
            .template-download td.error,
            .template-upload td.error {
                color: $color-warning-800;
                word-break: break-all;
            }
        }

        // Address
        &.alpaca-field-address .alpaca-field-address-mapcanvas {
            height: 250px;
        }

        // Image
        &.alpaca-field-image .alpaca-image-display {
            margin-top: $spacer;
        }
    }
}
