<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Cookie Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        .input-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JWT Cookie Test</h1>
        <p>This page tests JWT cookie functionality independently of Swagger UI.</p>
        
        <div class="input-group">
            <label for="jwtInput">JWT Token:</label>
            <input type="text" id="jwtInput" placeholder="Paste your JWT token here..." />
        </div>
        
        <div>
            <button onclick="setJwtCookie()">Set JWT Cookie</button>
            <button onclick="getJwtCookie()">Get JWT Cookie</button>
            <button onclick="clearJwtCookie()">Clear JWT Cookie</button>
            <button onclick="testSwaggerIntegration()">Test Swagger Integration</button>
        </div>
        
        <div id="result" class="result"></div>
        
        <h3>Quick Test Tokens:</h3>
        <div>
            <button onclick="useTestToken('APPLICANT')">Use APPLICANT Token</button>
            <button onclick="useTestToken('ADMIN')">Use ADMIN Token</button>
            <button onclick="useTestToken('SUPER_ADMIN')">Use SUPER_ADMIN Token</button>
        </div>
    </div>

    <script>
        // Test JWT tokens
        const testTokens = {
            'APPLICANT': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0X2FwcGxpY2FudF91c2VyIiwidXNlcklkIjoidGVzdF9hcHBsaWNhbnRfdXNlciIsInR5cGUiOiJhY2Nlc3MiLCJpc3MiOiJlZHVoay12bXMiLCJpYXQiOjE3NTMwNjM2OTksImV4cCI6MTc1MzA2NTQ5OSwiYXV0aCI6IkFQUExJQ0FOVCIsInJvbGUiOiJBUFBMSUNBTlQiLCJwZXJtaXNzaW9ucyI6WyJWSVNBX0FQUExJQ0FUSU9OIiwiRE9DVU1FTlRfVVBMT0FEIl19.uu5OKfQa8_Yu9jQc6cR75g-ouuWtF-Ne5Rarj3W0FcE',
            'ADMIN': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0X2FkbWluX3VzZXIiLCJ1c2VySWQiOiJ0ZXN0X2FkbWluX3VzZXIiLCJ0eXBlIjoiYWNjZXNzIiwiaXNzIjoiZWR1aGstdm1zIiwiaWF0IjoxNzUzMDYzNjk5LCJleHAiOjE3NTMwNjU0OTksImF1dGgiOiJBRE1JTiIsInJvbGUiOiJBRE1JTiIsInBlcm1pc3Npb25zIjpbIlZJU0FfU0VUVVAiLCJWSVNBX1ZBTElEQVRJT04iXX0.abc123def456',
            'SUPER_ADMIN': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0X3N1cGVyX2FkbWluX3VzZXIiLCJ1c2VySWQiOiJ0ZXN0X3N1cGVyX2FkbWluX3VzZXIiLCJ0eXBlIjoiYWNjZXNzIiwiaXNzIjoiZWR1aGstdm1zIiwiaWF0IjoxNzUzMDYzNjk5LCJleHAiOjE3NTMwNjU0OTksImF1dGgiOiJTVVBFUl9BRE1JTiIsInJvbGUiOiJTVVBFUl9BRE1JTiIsInBlcm1pc3Npb25zIjpbIlZJU0FfU0VUVVAiLCJWSVNBX1ZBTElEQVRJT04iLCJVU0VSX01BTkFHRU1FTlQiLCJTWVNURU1fQ09ORklHIl19.xyz789uvw012'
        };

        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.innerHTML = message;
            console.log(message);
        }

        function setJwtCookie() {
            const token = document.getElementById('jwtInput').value.trim();
            if (!token) {
                showResult('Please enter a JWT token', false);
                return;
            }

            try {
                // Set JWT cookie
                document.cookie = `jwt=${token}; path=/; max-age=7200; SameSite=Lax`;
                showResult(`✅ JWT cookie set successfully!<br>Token: ${token.substring(0, 50)}...`);
                
                // Also try to set it for Swagger UI
                localStorage.setItem('swagger-ui-auth', JSON.stringify({
                    bearerAuth: {
                        name: 'bearerAuth',
                        schema: {
                            type: 'http',
                            scheme: 'bearer'
                        },
                        value: token
                    }
                }));
                
                console.log('JWT set in both cookie and localStorage');
            } catch (error) {
                showResult(`❌ Error setting JWT cookie: ${error.message}`, false);
            }
        }

        function getJwtCookie() {
            const cookies = document.cookie.split(';');
            let jwtCookie = null;
            
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'jwt') {
                    jwtCookie = value;
                    break;
                }
            }

            if (jwtCookie) {
                showResult(`✅ JWT cookie found!<br>Value: ${jwtCookie.substring(0, 50)}...`);
                document.getElementById('jwtInput').value = jwtCookie;
            } else {
                showResult('❌ No JWT cookie found', false);
            }
        }

        function clearJwtCookie() {
            document.cookie = 'jwt=; path=/; max-age=0';
            localStorage.removeItem('swagger-ui-auth');
            document.getElementById('jwtInput').value = '';
            showResult('✅ JWT cookie and localStorage cleared');
        }

        function useTestToken(role) {
            const token = testTokens[role];
            document.getElementById('jwtInput').value = token;
            showResult(`✅ ${role} test token loaded`);
        }

        function testSwaggerIntegration() {
            const token = document.getElementById('jwtInput').value.trim();
            if (!token) {
                showResult('Please enter a JWT token first', false);
                return;
            }

            // Set cookie
            document.cookie = `jwt=${token}; path=/; max-age=7200; SameSite=Lax`;
            
            // Set localStorage for Swagger UI
            localStorage.setItem('swagger-ui-auth', JSON.stringify({
                bearerAuth: {
                    name: 'bearerAuth',
                    schema: {
                        type: 'http',
                        scheme: 'bearer'
                    },
                    value: token
                }
            }));

            showResult(`✅ JWT set for Swagger UI integration!<br>
                       Cookie: Set ✓<br>
                       localStorage: Set ✓<br>
                       <br>
                       Now go to <a href="/VMS/swagger-ui/index.html" target="_blank">Swagger UI</a> and check if the token is loaded.`);
        }

        // Auto-load current cookie on page load
        window.onload = function() {
            getJwtCookie();
        };
    </script>
</body>
</html>
