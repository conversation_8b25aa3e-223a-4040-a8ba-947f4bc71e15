<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple JWT Manager</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .role-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        .role-btn {
            background-color: #007bff;
            color: white;
        }
        .role-btn:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .clear-btn {
            background-color: #dc3545;
            color: white;
        }
        .clear-btn:hover {
            background-color: #c82333;
        }
        .link-btn {
            background-color: #28a745;
            color: white;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .link-btn:hover {
            background-color: #218838;
            color: white;
            text-decoration: none;
        }
        .current-token {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 11px;
            max-height: 100px;
            overflow-y: auto;
        }
        .status {
            padding: 12px;
            border-radius: 6px;
            margin: 15px 0;
            text-align: center;
            font-weight: 500;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .links {
            text-align: center;
            margin-top: 25px;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #0066cc;
        }
        .instructions h4 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Simple JWT Manager</h1>
        
        <div class="instructions">
            <h4>📋 How to use:</h4>
            <ol>
                <li>Click a role button to generate JWT token</li>
                <li>Go to Swagger UI and click "Authorize"</li>
                <li>Paste the generated token</li>
                <li>JWT will auto-sync to cookie when you use APIs</li>
            </ol>
        </div>
        
        <div class="section">
            <h3>Generate JWT Tokens</h3>
            <div class="role-buttons">
                <button class="role-btn" onclick="setToken('APPLICANT')">👤 APPLICANT</button>
                <button class="role-btn" onclick="setToken('ADMIN')">👨‍💼 ADMIN</button>
                <button class="role-btn" onclick="setToken('SUPER_ADMIN')">👑 SUPER_ADMIN</button>
            </div>
        </div>

        <div class="section">
            <h3>Current JWT Token</h3>
            <div id="currentToken" class="current-token">No token generated yet</div>
            <div style="text-align: center;">
                <button class="clear-btn" onclick="clearToken()">🗑️ Clear Cookie</button>
            </div>
        </div>

        <div id="status" class="status"></div>

        <div class="links">
            <a href="/VMS/swagger-ui/index.html" target="_blank" class="link-btn">📖 Open Swagger UI</a>
        </div>
    </div>

    <script>
        // Simple JWT generation (for demo purposes)
        const JWT_SECRET = '9bgAs07fmAahVzVJKS2sebqziHDHfceT';
        
        const ROLES = {
            'APPLICANT': {
                userId: 'test_applicant_user',
                role: 'APPLICANT',
                permissions: ['VISA_APPLICATION', 'DOCUMENT_UPLOAD']
            },
            'ADMIN': {
                userId: 'test_admin_user', 
                role: 'ADMIN',
                permissions: ['VISA_SETUP', 'VISA_VALIDATION']
            },
            'SUPER_ADMIN': {
                userId: 'test_super_admin_user',
                role: 'SUPER_ADMIN', 
                permissions: ['VISA_SETUP', 'VISA_VALIDATION', 'USER_MANAGEMENT', 'SYSTEM_CONFIG']
            }
        };

        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'status ' + (isError ? 'error' : 'success');
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 4000);
        }

        function setToken(role) {
            try {
                const config = ROLES[role];
                const now = Math.floor(Date.now() / 1000);
                
                // Create JWT payload
                const payload = {
                    sub: config.userId,
                    userId: config.userId,
                    type: 'access',
                    iss: 'eduhk-vms',
                    iat: now,
                    exp: now + 1800, // 30 minutes
                    auth: config.role,
                    role: config.role,
                    permissions: config.permissions
                };

                // Simple JWT generation (demo version)
                const header = btoa(JSON.stringify({alg: 'HS256', typ: 'JWT'})).replace(/[=]/g, '');
                const payloadStr = btoa(JSON.stringify(payload)).replace(/[=]/g, '');
                const signature = btoa('demo_signature_' + role).replace(/[=]/g, '').substring(0, 43);
                
                const jwt = `${header}.${payloadStr}.${signature}`;

                // Display the token
                document.getElementById('currentToken').textContent = jwt;
                
                // Also set it as cookie for immediate testing
                document.cookie = `jwt=${jwt}; path=/; max-age=1800`;
                
                showStatus(`✅ ${role} JWT token generated! Copy it to Swagger UI.`);
                
            } catch (error) {
                showStatus('❌ Error generating JWT: ' + error.message, true);
            }
        }

        function clearToken() {
            document.cookie = 'jwt=; path=/; max-age=0';
            document.getElementById('currentToken').textContent = 'No token generated yet';
            showStatus('🗑️ JWT cookie cleared');
        }

        function getCurrentJwtCookie() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'jwt') {
                    return value;
                }
            }
            return null;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const currentCookie = getCurrentJwtCookie();
            if (currentCookie) {
                document.getElementById('currentToken').textContent = currentCookie;
            }
        });
    </script>
</body>
</html>
