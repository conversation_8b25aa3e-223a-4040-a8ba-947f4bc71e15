
server.error.whitelabel.enabled=true
spring.thymeleaf.cache=false

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=40MB

spring.mail.host=rsmtp.ied.edu.hk
spring.mail.properties.mail.transport.protocol=smtp
spring.mail.properties.mail.smtp.port=25
spring.mail.properties.mail.smtp.auth=false

server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true
server.servlet.session.timeout=30m
# ?????????????????
server.connection-timeout=60000
server.tomcat.connection-timeout=60000
server.tomcat.keep-alive-timeout=60000

spring.jpa.properties.hibernate.query.plan_cache_max_size=1024
spring.jpa.properties.hibernate.query.plan_parameter_metadata_max_size=64
spring.jpa.properties.hibernate.query.in_clause_parameter_padding=true
spring.cloud.gcp.credentials.location=classpath:visionKey.json
chatgpt.api.key=DzC7yOuZQJTP2owXE1i6NeNSepX4GHbs

logging.level.org.thymeleaf=DEBUG
logging.level.org.springframework.web=DEBUG

spring.profiles.active=local

# CORS ?????????
management.endpoints.web.cors.allowed-origins=*
management.endpoints.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
management.endpoints.web.cors.allowed-headers=*

# ??????
server.tomcat.max-connections=8192
server.tomcat.accept-count=1000
server.tomcat.max-threads=200