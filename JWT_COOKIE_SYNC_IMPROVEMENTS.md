# JWT Cookie 同步改進總結

## 🎯 改進目標
將 Swagger UI 中的 JWT token 自動同步到 Cookie，而不需要硬編碼，實現動態 JWT 管理。

## ✅ 已完成的改進

### 1. **Cookie 路徑配置優化**
- **問題**: 原本 Cookie 路徑設置不一致，有些使用 `/`，有些使用 `/VMS`
- **解決方案**: 統一所有 Cookie 路徑為 `/VMS`，與應用程序上下文路徑一致
- **影響文件**:
  - `src/main/resources/application-local.properties`
  - `src/main/resources/static/swagger-jwt-manager.html`
  - `src/main/resources/static/js/swagger-jwt-sync.js`

### 2. **增強的 Swagger UI 配置**
- **新文件**: `src/main/java/com/eduhk/sa/config/swagger/SwaggerUIConfigImproved.java`
- **改進內容**:
  - 更可靠的 JWT 檢測邏輯（優先檢查 Swagger UI 內部狀態）
  - 增加 localStorage 檢查作為備用方案
  - 改進的 Cookie 設置（包含 SameSite 屬性）
  - 用戶友好的通知系統
  - 更強大的 DOM 監控機制

### 3. **Cookie 安全性增強**
- **SameSite 屬性**: 添加 `SameSite=Lax` 提高安全性
- **正確的過期時間**: 使用 2 小時（7200 秒）與 JWT 配置一致
- **路徑一致性**: 所有 Cookie 操作使用 `/VMS` 路徑

### 4. **測試工具**
- **新文件**: `src/main/resources/static/jwt-sync-test.html`
- **功能**:
  - 檢查當前 JWT 和 Cookie 狀態
  - 測試 Cookie 操作（設置、讀取、清除）
  - 測試同步功能
  - 調試工具（localStorage、Swagger UI、Cookies）

## 🔧 技術實現詳情

### JWT 檢測優先級
1. **Swagger UI 內部狀態** (最可靠)
   ```javascript
   window.ui.authSelectors.authorized().get('bearerAuth').get('value')
   ```

2. **localStorage 檢查** (備用方案)
   ```javascript
   localStorage.getItem('swagger-ui-auth')
   ```

3. **DOM 輸入框檢查** (最後手段)
   ```javascript
   document.querySelectorAll('input[type="text"], input[type="password"]')
   ```

### Cookie 設置格式
```javascript
document.cookie = `jwt=${token}; path=/VMS; max-age=7200; SameSite=Lax`;
```

### 自動同步機制
- **DOM 變化監控**: 使用 MutationObserver 監控 Swagger UI 變化
- **定期檢查**: 每 5 秒檢查一次同步狀態
- **事件監聽**: 監聽 Authorize/Logout 按鈕點擊事件

## 📁 修改的文件列表

### 配置文件
- `src/main/resources/application-local.properties` - Cookie 路徑配置

### Java 文件
- `src/main/java/com/eduhk/sa/config/swagger/SwaggerUIConfigImproved.java` - 新的增強配置

### HTML/JavaScript 文件
- `src/main/resources/static/swagger-jwt-manager.html` - Cookie 路徑修正
- `src/main/resources/static/js/swagger-jwt-sync.js` - 同步邏輯改進
- `src/main/resources/static/jwt-sync-test.html` - 新的測試工具

## 🚀 使用方法

### 1. 啟用改進的配置
將 `SwaggerUIConfig.java` 中的 Bean 名稱改為 `enhancedOpenApiCustomiser`，或者直接使用新的 `SwaggerUIConfigImproved.java`。

### 2. 測試同步功能
1. 訪問 `http://localhost:8080/VMS/jwt-sync-test.html`
2. 檢查當前狀態
3. 測試各種同步場景

### 3. 使用 JWT 管理頁面
1. 訪問 `http://localhost:8080/VMS/swagger-jwt-manager.html`
2. 生成所需角色的 JWT token
3. Token 會自動設置到正確的 Cookie 路徑

### 4. 在 Swagger UI 中使用
1. 訪問 `http://localhost:8080/VMS/swagger-ui/index.html`
2. 點擊 "Authorize" 按鈕
3. 貼上 JWT token
4. Token 會自動同步到 Cookie

## 🔍 驗證步驟

### 1. Cookie 路徑驗證
```javascript
// 在瀏覽器控制台執行
document.cookie.split(';').forEach(c => console.log(c.trim()));
```

### 2. 同步功能驗證
```javascript
// 檢查同步 API 是否可用
console.log(window.jwtSync);
// 手動觸發同步
window.jwtSync.sync();
```

### 3. Swagger UI 集成驗證
1. 在 Swagger UI 中設置 JWT
2. 檢查瀏覽器開發者工具的 Console 輸出
3. 確認 Cookie 已正確設置

## 🎉 預期效果

1. **無縫同步**: 在 Swagger UI 中設置的 JWT token 自動同步到 Cookie
2. **路徑一致**: 所有 Cookie 使用正確的 `/VMS` 路徑
3. **安全性**: 添加 SameSite 屬性提高安全性
4. **用戶友好**: 提供視覺反饋和測試工具
5. **可靠性**: 多種檢測方法確保同步成功

## 🔧 故障排除

### 如果同步不工作
1. 檢查瀏覽器控制台是否有錯誤
2. 使用測試工具檢查各組件狀態
3. 確認 Swagger UI 已完全載入
4. 檢查 Cookie 路徑是否正確

### 常見問題
- **Cookie 未設置**: 檢查路徑配置是否為 `/VMS`
- **同步延遲**: 正常現象，最多等待 5 秒
- **Token 格式錯誤**: 確保 JWT token 格式正確（以 `eyJ` 開頭）

## 📝 注意事項

1. 這些改進主要針對開發和測試環境
2. 生產環境請確保 JWT secret 的安全性
3. Cookie 過期時間與 JWT 配置保持一致
4. 建議定期測試同步功能的可靠性
